/* Helper function to set up our fonts */
webfont(family, file, weight=normal, style=normal) {
  @font-face {
    font-family: family;
    src: url('/fonts/' + file + ".eot");
    src: url('/fonts/' + file + ".eot?#iefix") format('embedded-opentype'),
         url('/fonts/' + file + ".woff") format('woff'),
         url('/fonts/' + file + ".ttf") format('truetype'),
         url('/fonts/' + file + ".svg#" + family) format('svg');
    font-weight: weight;
    font-style: style;
  }
}
/* Set up all the web-fonts we host */
webfont('engravers_gothic_fsregular', 'EngraversGothic-Regular-webfont');
webfont('volkswagen_serialregular', 'Volkswagen_Serial-Regular-webfont');

/* font and css for combining-diacritical things */
@font-face {
  font-family: 'combining-diacritical';
  src:  url('/fonts/combining-diacritical.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.cd {
  display: inline-block;
  font: normal normal normal 14px/1 combining-diacritical;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate(0, 0);
}




/* Basic style */
html, body {
  height: 100%;
  font-family: "Segoe UI", "Arial", "Helvetica", sans-serif;
  /* Prevent highlighting/selecting text */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}
.body { margin: 10px; }
.nav, .pagination, .carousel, .panel-title a { cursor: pointer; }

/* Wrapper for page content to push down footer */
#wrap {
  min-height: 100%;
  height: auto !important;
  height: 100%;
  margin: 0 auto -70px; /* Negative indent footer by its height */
  padding: 0 0 60px; /* Pad bottom by footer height */
}

/* Set the fixed height of the footer here */
#footer {
  height: 70px;
  background-color: #f5f5f5;
}

/* The keyboard background */
#keyboard {
  padding: 4px;
  border-radius: 3px;
  margin-top: 10px;
  margin-bottom: 10px;
  border: dashed 1px #ddd;
}
#keyboard #keyboard-bg {
  position: inherit;
  border: solid 1px #ddd;
  padding: 9px;
  box-sizing: content-box;
  border-radius: 6px;
  min-width: 56px;
  min-height: 56px;
}
#keyboard-bg div {
  position: absolute;
  border-color: black;
  box-sizing: border-box;
  background-clip: padding-box;
}
#keyboard .keyborder { border-color: black; border-style: solid; }
#keyboard .hover .keyborder, #keyboard .hover .decal .keyborder { border-color: green !important; border-style: solid; }
#keyboard .selected .keyborder, #keyboard .selected .decal .keyborder { border-color: red !important; border-style: solid; }
#keyboard .ghosted { opacity: 0.5; }
#keyboard .decal .keyborder { border-style: none !important; background: transparent !important; }
#keyboard .hover .decal .keyborder,
#keyboard .selected .decal .keyborder { border-style: dashed !important; border-width: 1px; }

/* Key labels */
.keylabel>div { display: table-cell; position: static !important; }
/* Vertical alignment */
.keylabel0>div, .keylabel1>div, .keylabel2>div { vertical-align: top; }
.keylabel3>div, .keylabel4>div, .keylabel5>div { vertical-align: middle; }
.keylabel6>div, .keylabel7>div, .keylabel8>div { vertical-align: bottom; }
.keylabel9, .keylabel10, .keylabel11 { top: 100%; margin-top: -1px; font-size: 10px !important; max-height: 1em !important; white-space: nowrap; overflow: hidden; }
/* Horizontal  alignment */
.keylabel0>div, .keylabel3>div, .keylabel6>div, .keylabel9>div { text-align: left; }
.keylabel1>div, .keylabel4>div, .keylabel7>div, .keylabel10>div { text-align: center; }
.keylabel2>div, .keylabel5>div, .keylabel8>div, .keylabel11>div { text-align: right; }
/* Label Sizes */
for i in (1..9) {
  .keylabel.textsize{i} {
    font-size: (6+2*i)px;
    line-height: 1em;
  }
}
.keylabels { font-family: "Helvetica", "Arial", sans-serif; }
.keylabel hr { display: inline; }
.keylabel hr:before {
  position: relative;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  content: "\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500\2500";
}

/* Keycap-Profile Modifications */
.SA .keylabels, .DSA .keylabels {
  font-family: 'engravers_gothic_fsregular'; /* substitute for Gorton Modified */
}

.DSA .keylabel9, .DSA .keylabel10, .DSA .keylabel11,
.SA .keylabel9, .SA .keylabel10, .SA .keylabel11 {
  margin-top: -2px;
  font-size: 9px !important;
}
.DCS .keytop, .OEM .keytop {
  background: linear-gradient(to right, #00000000 0%, #0000001A 40%, #0000001A 60%, #00000000 100%);
  background-repeat: no-repeat;
}
.DSA .keytop, .SA .keytop {
  background: radial-gradient(ellipse at center, #0000001A 0%, #0000001A 10%, #00000000 100%);
  background-repeat: no-repeat;
}

.DCS.SPACE .keytop, .OEM.SPACE .keytop, .DSA.SPACE .keytop, .SA.SPACE .keytop {
  background: linear-gradient(to bottom, #0000001A 0%, #00000000 20%, #00000000 40%, #0000001A 100%);
  background-repeat: no-repeat;
}
.DSA.DISH .keytop, .DSA.HOMING .keytop, .SA.HOMING .keytop {
  background: radial-gradient(circle, #0000001C 50%, #0000000A 60%);
  background-repeat: no-repeat;
}
.HOMING .keylabels {
  background-image: url("nub.png");
  background-repeat: no-repeat;
  background-position: center 90%;
}
.DSA.HOMING .keylabels, .SA.HOMING .keylabels { background-image: none; }

.FLAT .keytop {
  display: none;
}
.CHICKLET .keylabel9, .CHICKLET .keylabel10, .CHICKLET .keylabel11,
.FLAT .keylabel9, .FLAT .keylabel10, .FLAT .keylabel11 {
  display: none;
}

/* color swatches */
#swatches, #glyphs { list-style-type: none; margin: 0px; padding: 0px; }
.swatch, .glyph {
  display: inline-block;
  position: relative;
  width: 16px;
  height: 32px;
  border: solid 1px #888;
  margin-right: -1px;
  margin-bottom: -6px;
  padding: 0px;
}
.glyph {font-family: 'combining-diacritical';
  width: 1.7em;
  text-align: center;
  padding-top: 0px;
  font-size: 1.618em;
  border: solid 1px #eee;
  background-color: #fff;
}
.glyph:hover { background-color: #eee; }
.glyph.selected { background-color: #eef !important; }

#glyphScroller {
  width: 100%;
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  max-height: 230px;
  border: solid 1px #ccc;
  padding: 4px;
}

#swatches.disabled .swatch { background-color: rgb(235,235,228) !important; }

.swatch .highlight { display: none; }
.swatch.selected-bg .highlight.bg { display: block; z-index: 100; width: 6px; height: 6px; border: solid 1px black; background: white; opacity: 0.75; position: absolute; left: 7px; top: 23px; }
.swatch.selected-fg .highlight.fg { display: block; z-index: 100; width: 6px; height: 6px; border: solid 1px white; background: black; opacity: 0.75; position: absolute; left: 1px; top: 23px; }

/* general styles */
.hidden { display: none; }
.error { border: solid 2px rgb(217,83,79) !important; }
#selectionRectangle { position: absolute; border: dashed 2px red; }
#rotationCrosshairs {
  display: none;
  position: absolute;
  margin: 0px;
  padding: 0px;
  font-size: 25px;
  line-height: 23px;
  width: 25px;
  height: 25px;
  color: black;
  text-shadow: -1px 0 white, 0 1px white, 1px 0 white, 0 -1px white;
}

#rawdata, #rawdata-error, #customstyles, #customstyles-error, #noteseditor, #picker-html, #summary { font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace; line-height: 1.42857143 !important; }
#rawdata, #rawdata-error, #customstyles, #customstyles-error, #noteseditor, #picker-html { border: solid 1px #ccc; }

#rawdata, #customstyles { width: 100%; height: 400px; }
#noteseditor { height: 275px; }
#rawdata-error, #customstyles-error { white-space: pre; }
#summary { margin-left: 20px; padding-left: 20px; }

#tab-content {
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    padding-top: 15px;
    padding-bottom: 15px;
    margin-left: 0px;
}
#properties.row { padding-left: 30px; }

/* Tooltip modifications */
.hint--rounded:after { width: 300px; }
.hint:after, [data-hint]:after { white-space: pre-line; }
.hint:before, .hint:after, [data-hint]:before, [data-hint]:after { -webkit-transition: 0.05s ease; -moz-transition: 0.05s ease; transition: 0.05s ease; }
.hint--no-animate:before, .hint--no-animate:after { transition: 0ms !important; }

/* Help Dialog */
.modal-xl .modal-dialog { width: 80vw; }
.modal-xxl .modal-dialog { width: 90vw; }

/* Our "README" dialog; we want the body of the dialog itself to be scrollable */
.markdownDialog .modal-dialog { margin: 30px auto; } /*60px*/
.markdownDialog .modal-header { height: 55px; }
.markdownDialog .modal-footer { height: 74px; margin-top: 0px; }
.markdownDialog .modal-body {
  padding-bottom: 15px;
  height: calc(100vh - 205px) !important;
  overflow-y: auto !important;
}

.drag-over { border: dotted 3px red !important; box-sizing: border-box; }

/* Only show the keyboard layout itself when printing */
@media print {
  html, body { overflow: hidden; height: 100%; margin: 0 !important; padding: 0 !important; }
  #wrap > .navbar, #wrap > .body > .btn-group, #keyboard ~ *, #keyboard + *, #footer, #helpDialog, #markdownDialog, #summary { display: none; margin: 0 !important; padding: 0 !important; }
  div.body { margin: 0 !important; padding: 0 !important; }
  a { color: black; text-decoration: none; }
  #keyboard {
    border: none;
    background-color: inherit !important;
    transform: scale(0.75,0.75);
    position: absolute !important;
    left: 0px;
    top: 0px;
    padding-left: 0px; padding-top: 0px;
  }
}

/* the default (-15px) causes overflow problems */
.navbar-right { margin-right: 0; }

/* densely pack the form */
.form-horizontal .form-inline, .form-horizontal > .form-group.form-group-sm > .form-outdent { margin-left: -15px; }
.form-horizontal .form-inline .form-group { margin-left: 0; margin-right: 0; }
.form-group { margin-bottom: 2px; }
.form-control.input-sm, .input-group-sm > .form-control { height: 26px; padding: 2px; }
.input-group-sm > .input-group-btn > .btn { height: 26px; padding: 2px; min-width: 16px; }
.input-group-sm > .input-group-addon { height: 26px; padding: 2px; min-width: 16px; }
.form-horizontal .form-group-sm .checkbox { padding-top: 3px; min-height: 26px; }

#swap-colors, #swap-sizes { position: absolute; top: 11px; left: 200px; z-index: 1; }
.color-name { display: inline-block; min-width: 3em; font-size: 10px; white-space: nowrap; }

.use-default:after {
  content: "\d7"; /* &times; */
  right: 0px;
  bottom: -4px;
  position: absolute;
}

/* Combining rectangle glyph */
.combine-rect:before {
  content: '\25AD';
  font-style: normal;
  /*margin: 0 -0.66em 0 -0.33em;*/
}

.avatar {
  max-width: 1.25em;
  max-height: 1.25em;
}

/* nested dropdown menu lists; see bootstrap.css */
.dropdown-menu ul {
  list-style: none;
  margin: 2px 0 0;
  padding: 5px 0;
}
.dropdown-menu ul > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}
.dropdown-menu ul > li > a:hover,
.dropdown-menu ul > li > a:focus {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5;
}
/* button size between sm & xs; meant for inline forms */
.btn-sm-form,
.btn-group-sm-form > .btn {
  padding: 3px 8px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}


.dropdown-fixedwidth {
  width:110px;
  max-width:110px;
  height: 26px;
}
.dropdown-fixedwidth>:first-child {
  display: block;
  left: 5px;
  width: calc(100% - 12px);
  text-align: left;
  overflow: hidden;
}
.dropdown-fixedwidth .caret {
  display: block;
  position: absolute;
  right: 5px;
  top: 11px;
}

.dropdown-select {
  max-height: 40vh;
  overflow-y: auto;
}

.dropdown-fixedwidth:disabled {
  background-color: #eee !important;
}

table.summarytable {
  border-collapse: collapse;
  margin-top: 1em;
}

table.summarytable th, table.summarytable td {
  border: 1px solid black;
  padding: 2px 5px;
}

settings-key(size, adjust = 0) {
  position: relative;
  width: size px;
  height: size px;
  float: left;

  outerPad = 6
  innerPad = 3
  innerSize = (size - outerPad * 2)
  labelSize = (innerSize - innerPad * 2)

  & .keyborder {
    position: absolute;
    width: size px;
    height: size px;
    border-style: solid;
    border-width: 1px;
    border-color: black;
    border-radius: 5px;
    background-color: #cccccc;
    pointer-events: none;
  }
  & .keylabels {
    position: absolute;
    left: outerPad px;
    top: 3px;
    width: innerSize px;
    height: (innerSize - adjust) px;
    padding: innerPad px;
    border-style: solid;
    border-width: 1px;
    border-color: rgba(0,0,0,0.1);
    background-color: #fcfcfc;
    border-radius: 3px;
    pointer-events: none;
  }
  & .keylabel {
    position: absolute;
    width: labelSize px;
    height: (labelSize - adjust) px;
    pointer-events: none;
  }
  & .keylabel div {
    position: absolute;
    width: labelSize px;
    max-width: labelSize px;
    height: (labelSize - adjust) px;
    pointer-events: none;
  }
  & .keylabel div * { 
    pointer-events: all;
  }
}

#label-move-src, #label-move-dst {
  settings-key(108, 8);
  .keylabel9, .keylabel10, .keylabel11 { overflow: visible; }
  input { z-index: 1; }
}

#text-align {
  settings-key(108);
  .keylabel button {
    display: inline-block;
    font-size: 16px;
    width: 28px;
    z-index: 1;
    float: none;
  }
}
